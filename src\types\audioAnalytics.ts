// Audio Analytics Types and Interfaces

export interface AudioMetadata {
  title: string;
  duration: number;
  url: string;
  blogSlug?: string;
  podcastEpisodeId?: string;
  podcastSeriesId?: string;
  contentType: 'blog' | 'podcast';
}

export interface AudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  buffered: TimeRanges | null;
  seeked: boolean;
  ended: boolean;
  error: MediaError | null;
}

export interface AudioAnalyticsEvent {
  event: string;
  event_category: 'audio_player';
  event_action: AudioEventAction;
  event_label: string;
  custom_parameters?: {
    audio_title?: string;
    audio_duration?: number;
    audio_current_time?: number;
    audio_progress_percentage?: number;
    audio_url?: string;
    blog_slug?: string;
    podcast_episode_id?: string;
    podcast_series_id?: string;
    content_type?: 'blog' | 'podcast';
    user_agent?: string;
    timestamp?: number;
    session_id?: string;
    error_message?: string;
    error_code?: number;
    seek_from?: number;
    seek_to?: number;
    playback_rate?: number;
    volume_level?: number;
    total_listening_time?: number;
    engagement_score?: number;
    ga_client_id?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    clarity?: string;
    referrer?: string;
    play_count?: number;
    pause_count?: number;
    seek_count?: number;
    seek_distance?: number;
    milestone?: number;
    download_url?: string;
    session_duration?: number;
    max_progress_reached?: number;
    milestones_achieved?: number;
  };
}

export type AudioEventAction =
  | 'play'
  | 'pause'
  | 'ended'
  | 'progress_25'
  | 'progress_50'
  | 'progress_75'
  | 'progress_100'
  | 'seek'
  | 'volume_change'
  | 'playback_rate_change'
  | 'download_attempt'
  | 'error'
  | 'loaded'
  | 'buffer_start'
  | 'buffer_end'
  | 'session_start'
  | 'session_end';

export interface AudioSessionData {
  sessionId: string;
  startTime: number;
  endTime?: number;
  totalListeningTime: number;
  progressMilestones: {
    '25%': boolean;
    '50%': boolean;
    '75%': boolean;
    '100%': boolean;
  };
  seekEvents: number;
  pauseEvents: number;
  playEvents: number;
  maxProgressReached: number;
  engagementScore: number;
}

export interface AudioAnalyticsConfig {
  enableProgressTracking: boolean;
  enableSeekTracking: boolean;
  enableVolumeTracking: boolean;
  enableErrorTracking: boolean;
  enableSessionTracking: boolean;
  progressMilestones: number[]; // [25, 50, 75, 100]
  debounceTime: number; // milliseconds
  sessionTimeout: number; // milliseconds
}

export interface GTMDataLayer {
  event: string;
  event_category: string;
  event_action: string;
  event_label: string;
  custom_parameters?: Record<string, any>;
}

declare global {
  interface Window {
    dataLayer?: GTMDataLayer[];
    gtag?: (...args: any[]) => void;
  }
}
