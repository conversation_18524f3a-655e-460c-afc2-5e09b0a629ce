import {
  AudioMetadata,
  AudioAnalyticsEvent,
  AudioEventAction,
  AudioSessionData,
  AudioAnalyticsConfig,
  GTMDataLayer,
} from '../types/audioAnalytics';
import getUserTrackingData from '../common/getUserTrackingData';

class AudioAnalytics {
  private metadata: AudioMetadata;
  private config: AudioAnalyticsConfig;
  private sessionData: AudioSessionData;
  private progressMilestones: Set<number>;
  private lastProgressUpdate: number;
  private debounceTimer: NodeJS.Timeout | null;
  private sessionTimer: NodeJS.Timeout | null;

  constructor(metadata: AudioMetadata, config?: Partial<AudioAnalyticsConfig>) {
    this.metadata = metadata;
    this.config = {
      enableProgressTracking: true,
      enableSeekTracking: true,
      enableVolumeTracking: true,
      enableErrorTracking: true,
      enableSessionTracking: true,
      progressMilestones: [25, 50, 75, 100],
      debounceTime: 1000,
      sessionTimeout: 30000,
      ...config,
    };

    this.progressMilestones = new Set();
    this.lastProgressUpdate = 0;
    this.debounceTimer = null;
    this.sessionTimer = null;

    this.initializeSession();
  }

  private initializeSession(): void {
    this.sessionData = {
      sessionId: this.generateSessionId(),
      startTime: Date.now(),
      totalListeningTime: 0,
      progressMilestones: {
        '25%': false,
        '50%': false,
        '75%': false,
        '100%': false,
      },
      seekEvents: 0,
      pauseEvents: 0,
      playEvents: 0,
      maxProgressReached: 0,
      engagementScore: 0,
    };

    if (this.config.enableSessionTracking) {
      this.trackEvent('session_start', {
        session_id: this.sessionData.sessionId,
      });
    }
  }

  private generateSessionId(): string {
    return `audio_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private async trackEvent(action: AudioEventAction, customParams: Record<string, any> = {}): Promise<void> {
    try {
      const userTrackingData = await getUserTrackingData();

      const event: AudioAnalyticsEvent = {
        event: 'audio_player_interaction',
        event_category: 'audio_player',
        event_action: action,
        event_label: this.metadata.title,
        custom_parameters: {
          audio_title: this.metadata.title,
          audio_duration: this.metadata.duration,
          audio_url: this.metadata.url,
          content_type: this.metadata.contentType,
          blog_slug: this.metadata.blogSlug,
          podcast_episode_id: this.metadata.podcastEpisodeId,
          podcast_series_id: this.metadata.podcastSeriesId,
          user_agent: navigator.userAgent,
          timestamp: Date.now(),
          session_id: this.sessionData.sessionId,
          ga_client_id: userTrackingData.ga_client_id,
          utm_source: userTrackingData.utm_source,
          utm_medium: userTrackingData.utm_medium,
          utm_campaign: userTrackingData.utm_campaign,
          clarity: userTrackingData.clarity,
          ...customParams,
        },
      };

      // Send to GTM DataLayer
      this.sendToGTM(event);

      // Log for debugging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Audio Analytics Event:', event);
      }
    } catch (error) {
      console.error('Error tracking audio event:', error);
    }
  }

  private sendToGTM(event: AudioAnalyticsEvent): void {
    try {
      if (typeof window !== 'undefined' && window.dataLayer) {
        const gtmEvent: GTMDataLayer = {
          event: event.event,
          event_category: event.event_category,
          event_action: event.event_action,
          event_label: event.event_label,
          custom_parameters: event.custom_parameters,
        };

        window.dataLayer.push(gtmEvent);
      }
    } catch (error) {
      console.error('Error sending event to GTM:', error);
    }
  }

  private debounce(func: Function, delay: number): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.debounceTimer = setTimeout(() => func(), delay);
  }

  private calculateEngagementScore(): number {
    const progressScore = (this.sessionData.maxProgressReached / 100) * 40;
    const milestoneScore = Object.values(this.sessionData.progressMilestones).filter(Boolean).length * 10;
    const listeningTimeScore = Math.min((this.sessionData.totalListeningTime / this.metadata.duration) * 30, 30);
    const interactionPenalty = Math.max(0, (this.sessionData.seekEvents + this.sessionData.pauseEvents) * -2);

    return Math.max(0, Math.min(100, progressScore + milestoneScore + listeningTimeScore + interactionPenalty));
  }

  // Public methods for audio player events
  public onPlay(currentTime: number): void {
    this.sessionData.playEvents++;
    this.trackEvent('play', {
      audio_current_time: currentTime,
      play_count: this.sessionData.playEvents,
    });

    // Reset session timeout
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
    }
  }

  public onPause(currentTime: number): void {
    this.sessionData.pauseEvents++;
    this.trackEvent('pause', {
      audio_current_time: currentTime,
      pause_count: this.sessionData.pauseEvents,
    });

    // Start session timeout
    this.sessionTimer = setTimeout(() => {
      this.endSession();
    }, this.config.sessionTimeout);
  }

  public onTimeUpdate(currentTime: number, duration: number): void {
    if (!this.config.enableProgressTracking || duration === 0) return;

    const progressPercentage = (currentTime / duration) * 100;
    this.sessionData.maxProgressReached = Math.max(this.sessionData.maxProgressReached, progressPercentage);

    // Update total listening time
    const now = Date.now();
    if (this.lastProgressUpdate > 0) {
      this.sessionData.totalListeningTime += now - this.lastProgressUpdate;
    }
    this.lastProgressUpdate = now;

    // Check for milestone achievements
    this.config.progressMilestones.forEach(milestone => {
      if (progressPercentage >= milestone && !this.progressMilestones.has(milestone)) {
        this.progressMilestones.add(milestone);
        const milestoneKey = `${milestone}%` as keyof typeof this.sessionData.progressMilestones;
        this.sessionData.progressMilestones[milestoneKey] = true;

        this.debounce(() => {
          this.trackEvent(`progress_${milestone}` as AudioEventAction, {
            audio_current_time: currentTime,
            audio_progress_percentage: progressPercentage,
            milestone: milestone,
          });
        }, this.config.debounceTime);
      }
    });
  }

  public onSeek(fromTime: number, toTime: number): void {
    if (!this.config.enableSeekTracking) return;

    this.sessionData.seekEvents++;
    this.trackEvent('seek', {
      seek_from: fromTime,
      seek_to: toTime,
      seek_distance: Math.abs(toTime - fromTime),
      seek_count: this.sessionData.seekEvents,
    });
  }

  public onVolumeChange(volume: number): void {
    if (!this.config.enableVolumeTracking) return;

    this.debounce(() => {
      this.trackEvent('volume_change', {
        volume_level: volume,
      });
    }, this.config.debounceTime);
  }

  public onPlaybackRateChange(playbackRate: number): void {
    this.trackEvent('playback_rate_change', {
      playback_rate: playbackRate,
    });
  }

  public onEnded(currentTime: number): void {
    this.sessionData.progressMilestones['100%'] = true;
    this.sessionData.maxProgressReached = 100;

    this.trackEvent('ended', {
      audio_current_time: currentTime,
      total_listening_time: this.sessionData.totalListeningTime,
      engagement_score: this.calculateEngagementScore(),
    });

    this.endSession();
  }

  public onError(error: MediaError): void {
    if (!this.config.enableErrorTracking) return;

    this.trackEvent('error', {
      error_code: error.code,
      error_message: this.getErrorMessage(error.code),
    });
  }

  public onLoadedData(duration: number): void {
    this.metadata.duration = duration;
    this.trackEvent('loaded', {
      audio_duration: duration,
    });
  }

  public onDownloadAttempt(): void {
    this.trackEvent('download_attempt', {
      download_url: this.metadata.url,
    });
  }

  private getErrorMessage(errorCode: number): string {
    const errorMessages = {
      1: 'MEDIA_ERR_ABORTED - The user aborted the media playback',
      2: 'MEDIA_ERR_NETWORK - A network error occurred while fetching the media',
      3: 'MEDIA_ERR_DECODE - An error occurred while decoding the media',
      4: 'MEDIA_ERR_SRC_NOT_SUPPORTED - The media format is not supported',
    };
    return errorMessages[errorCode] || `Unknown error (code: ${errorCode})`;
  }

  public endSession(): void {
    if (!this.config.enableSessionTracking) return;

    this.sessionData.endTime = Date.now();
    this.sessionData.engagementScore = this.calculateEngagementScore();

    this.trackEvent('session_end', {
      session_duration: this.sessionData.endTime - this.sessionData.startTime,
      total_listening_time: this.sessionData.totalListeningTime,
      engagement_score: this.sessionData.engagementScore,
      max_progress_reached: this.sessionData.maxProgressReached,
      milestones_achieved: Object.values(this.sessionData.progressMilestones).filter(Boolean).length,
    });

    // Clear timers
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
    }
  }

  public destroy(): void {
    this.endSession();
  }
}

export default AudioAnalytics;
