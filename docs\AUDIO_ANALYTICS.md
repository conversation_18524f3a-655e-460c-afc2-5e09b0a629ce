# Audio Analytics Implementation

This document describes the comprehensive audio analytics tracking system implemented for blog and podcast audio players in the MTL Next.js site.

## Overview

The audio analytics system tracks user interactions with audio players across blog posts and podcast episodes, providing detailed insights into user engagement, listening patterns, and content performance.

## Architecture

### Components

1. **AudioAnalytics Utility** (`src/utils/audioAnalytics.ts`)
   - Core analytics tracking logic
   - Event handling and data processing
   - GTM integration
   - Session management

2. **AnalyticsAudioPlayer Component** (`src/components/AnalyticsAudioPlayer/`)
   - Enhanced HTML5 audio player wrapper
   - Automatic event binding
   - Analytics integration
   - Maintains existing styling and functionality

3. **Type Definitions** (`src/types/audioAnalytics.ts`)
   - TypeScript interfaces and types
   - Event structure definitions
   - Configuration options

## Tracked Events

### Core Events
- **play**: Audio playback started
- **pause**: Audio playback paused
- **ended**: Audio playback completed
- **loaded**: Audio metadata loaded
- **error**: Audio loading/playback error

### Progress Events
- **progress_25**: 25% of audio completed
- **progress_50**: 50% of audio completed
- **progress_75**: 75% of audio completed
- **progress_100**: 100% of audio completed

### Interaction Events
- **seek**: User jumped to different position
- **volume_change**: Volume level changed
- **playback_rate_change**: Playback speed changed
- **download_attempt**: User attempted to download audio

### Session Events
- **session_start**: Analytics session initiated
- **session_end**: Analytics session terminated

## Event Data Structure

Each event includes:

```typescript
{
  event: 'audio_player_interaction',
  event_category: 'audio_player',
  event_action: 'play' | 'pause' | 'seek' | ...,
  event_label: 'Audio Title',
  custom_parameters: {
    audio_title: string,
    audio_duration: number,
    audio_current_time: number,
    audio_url: string,
    content_type: 'blog' | 'podcast',
    blog_slug?: string,
    podcast_episode_id?: string,
    podcast_series_id?: string,
    session_id: string,
    engagement_score: number,
    // ... additional tracking data
  }
}
```

## Usage

### Blog Components

```tsx
import AnalyticsAudioPlayer from '@components/AnalyticsAudioPlayer';

<AnalyticsAudioPlayer
  src={blogData?.audio_file?.data?.attributes?.url}
  title={blogData?.title}
  blogSlug={blogData?.slug}
  contentType="blog"
  className={styles.audio_player}
  controls
  controlsList="nodownload"
/>
```

### Podcast Components

```tsx
<AnalyticsAudioPlayer
  src={episode?.podcast_audio_file?.data?.attributes?.url}
  title={episode?.title}
  podcastEpisodeId={episode?.id?.toString()}
  podcastSeriesId={series?.id?.toString()}
  contentType="podcast"
  className={styles.audio_player}
  controls
  controlsList="nodownload"
/>
```

## Configuration

The analytics system supports customization:

```typescript
const analyticsConfig = {
  enableProgressTracking: true,
  enableSeekTracking: true,
  enableVolumeTracking: true,
  enableErrorTracking: true,
  enableSessionTracking: true,
  progressMilestones: [25, 50, 75, 100],
  debounceTime: 1000,
  sessionTimeout: 30000,
};
```

## Engagement Scoring

The system calculates an engagement score (0-100) based on:
- **Progress Score (40%)**: How far the user listened
- **Milestone Score (40%)**: Number of milestones reached
- **Listening Time Score (30%)**: Total time spent listening
- **Interaction Penalty**: Deductions for excessive seeking/pausing

## GTM Integration

Events are automatically sent to Google Tag Manager via the `dataLayer`:

```javascript
window.dataLayer.push({
  event: 'audio_player_interaction',
  event_category: 'audio_player',
  event_action: 'play',
  event_label: 'Blog Title',
  custom_parameters: { /* event data */ }
});
```

## Testing

Comprehensive test suites are included:

- **Component Tests**: `src/components/AnalyticsAudioPlayer/__tests__/`
- **Utility Tests**: `src/utils/__tests__/audioAnalytics.test.ts`

Run tests:
```bash
npm test -- --testPathPattern=audioAnalytics
```

## Performance Considerations

- **Debouncing**: Rapid events (time updates, volume changes) are debounced
- **Session Management**: Automatic cleanup prevents memory leaks
- **Error Handling**: Graceful degradation when analytics fail
- **Lazy Loading**: Analytics only initialize when audio is present

## Browser Compatibility

- Modern browsers with HTML5 audio support
- Graceful degradation for older browsers
- No impact on core audio functionality

## Privacy & GDPR

- Respects existing cookie consent mechanisms
- No PII collection
- Anonymized session tracking
- Configurable tracking levels

## Monitoring & Debugging

Development mode includes:
- Console logging of all events
- Error reporting
- Performance metrics
- Session state visibility

## Migration Notes

The implementation maintains backward compatibility:
- Existing audio players continue to work
- No breaking changes to component APIs
- Gradual rollout possible
- Easy to disable if needed

## Future Enhancements

Potential improvements:
- A/B testing integration
- Real-time analytics dashboard
- Advanced engagement metrics
- Cross-device session tracking
- Content recommendation based on listening patterns
