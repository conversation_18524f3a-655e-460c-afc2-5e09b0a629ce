'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import AudioAnalytics from '../../utils/audioAnalytics';
import {
  AudioMetadata,
  AudioAnalyticsConfig,
} from '../../types/audioAnalytics';

interface AnalyticsAudioPlayerProps {
  src: string;
  title: string;
  className?: string;
  controls?: boolean;
  controlsList?: string;
  blogSlug?: string;
  podcastEpisodeId?: string;
  podcastSeriesId?: string;
  contentType?: 'blog' | 'podcast';
  analyticsConfig?: Partial<AudioAnalyticsConfig>;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  onError?: (error: MediaError) => void;
  onLoadedData?: () => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
}

const AnalyticsAudioPlayer: React.FC<AnalyticsAudioPlayerProps> = ({
  src,
  title,
  className = '',
  controls = true,
  controlsList = 'nodownload',
  blogSlug,
  podcastEpisodeId,
  podcastSeriesId,
  contentType = 'blog',
  analyticsConfig,
  onPlay,
  onPause,
  onEnded,
  onError,
  onLoadedData,
  onTimeUpdate,
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const analyticsRef = useRef<AudioAnalytics | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const lastCurrentTime = useRef<number>(0);

  // Initialize analytics when component mounts and src is available
  useEffect(() => {
    if (!src || !title || isInitialized) return;

    const metadata: AudioMetadata = {
      title,
      duration: 0, // Will be updated when audio loads
      url: src,
      blogSlug,
      podcastEpisodeId,
      podcastSeriesId,
      contentType,
    };

    analyticsRef.current = new AudioAnalytics(metadata, analyticsConfig);
    setIsInitialized(true);

    return () => {
      if (analyticsRef.current) {
        analyticsRef.current.destroy();
        analyticsRef.current = null;
      }
    };
  }, [
    src,
    title,
    blogSlug,
    podcastEpisodeId,
    podcastSeriesId,
    contentType,
    analyticsConfig,
    isInitialized,
  ]);

  // Handle play event
  const handlePlay = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onPlay(audio.currentTime);
    }
    onPlay?.();
  }, [onPlay]);

  // Handle pause event
  const handlePause = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onPause(audio.currentTime);
    }
    onPause?.();
  }, [onPause]);

  // Handle time update event
  const handleTimeUpdate = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onTimeUpdate(audio.currentTime, audio.duration);
      onTimeUpdate?.(audio.currentTime, audio.duration);
    }
  }, [onTimeUpdate]);

  // Handle seek event
  const handleSeeked = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onSeek(lastCurrentTime.current, audio.currentTime);
      lastCurrentTime.current = audio.currentTime;
    }
  }, []);

  // Handle volume change event
  const handleVolumeChange = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onVolumeChange(audio.volume);
    }
  }, []);

  // Handle playback rate change event
  const handleRateChange = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onPlaybackRateChange(audio.playbackRate);
    }
  }, []);

  // Handle ended event
  const handleEnded = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onEnded(audio.currentTime);
    }
    onEnded?.();
  }, [onEnded]);

  // Handle error event
  const handleError = useCallback(() => {
    const audio = audioRef.current;
    if (audio && audio.error && analyticsRef.current) {
      analyticsRef.current.onError(audio.error);
    }
    if (audio?.error) {
      onError?.(audio.error);
    }
  }, [onError]);

  // Handle loaded data event
  const handleLoadedData = useCallback(() => {
    const audio = audioRef.current;
    if (audio && analyticsRef.current) {
      analyticsRef.current.onLoadedData(audio.duration);
    }
    onLoadedData?.();
  }, [onLoadedData]);

  // Handle seeking event (before seek completes)
  const handleSeeking = useCallback(() => {
    const audio = audioRef.current;
    if (audio) {
      lastCurrentTime.current = audio.currentTime;
    }
  }, []);

  // Handle download attempt (when user right-clicks or tries to download)
  const handleContextMenu = useCallback((_e: React.MouseEvent) => {
    if (analyticsRef.current) {
      analyticsRef.current.onDownloadAttempt();
    }
  }, []);

  // Update last current time for seek detection
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      const updateLastTime = () => {
        lastCurrentTime.current = audio.currentTime;
      };

      audio.addEventListener('loadeddata', updateLastTime);
      return () => {
        audio.removeEventListener('loadeddata', updateLastTime);
      };
    }
  }, []);

  return (
    <audio
      ref={audioRef}
      className={className}
      src={src}
      controls={controls}
      controlsList={controlsList}
      onPlay={handlePlay}
      onPause={handlePause}
      onTimeUpdate={handleTimeUpdate}
      onSeeked={handleSeeked}
      onSeeking={handleSeeking}
      onVolumeChange={handleVolumeChange}
      onRateChange={handleRateChange}
      onEnded={handleEnded}
      onError={handleError}
      onLoadedData={handleLoadedData}
      onContextMenu={handleContextMenu}
      preload="metadata"
    />
  );
};

export default AnalyticsAudioPlayer;
